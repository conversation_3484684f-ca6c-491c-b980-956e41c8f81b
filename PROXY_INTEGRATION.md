# Figma Plugin Anthropic Proxy Integration

## Overview
This document outlines the changes made to integrate the Figma plugin with a custom Anthropic proxy server instead of directly calling the Anthropic API.

## Changes Made

### 1. AIService Refactoring (`packages/backend/src/services/aiService.ts`)

**Before:**
- Used `@anthropic-ai/sdk` to make direct API calls to Anthropic
- Required `ANTHROPIC_API_KEY` environment variable
- Made direct calls to `https://api.anthropic.com`

**After:**
- Removed dependency on `@anthropic-ai/sdk`
- Implemented JWT-based authentication with proxy server
- Makes HTTP requests to custom proxy server at `http://localhost:5173`
- Automatic token management with 15-minute expiry and refresh logic
- Secure token storage using Figma's `clientStorage`

### 2. Authentication Flow

**JWT Token Management:**
- Fetches tokens from `/api/auth/token` endpoint
- Stores tokens securely in Figma's clientStorage
- Automatically refreshes expired tokens
- Includes 1-minute buffer before token expiry

**Token Storage:**
```javascript
{
  token: "jwt_token_string",
  expiry: timestamp_in_milliseconds
}
```

### 3. API Request Changes

**Endpoint:** `/api/clean-code`
**Method:** POST
**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer {jwt_token}`

**Request Body:**
```javascript
{
  code: "generated_code_string",
  imageUrl: "base64_image_data",
}
```

**Response Format:**
```javascript
{
  cleanCode: "processed_code_string",
  suggestions?: ["error_message_1", "error_message_2"]
}
```

### 4. Network Access Configuration (`manifest.json`)

**Updated allowed domains:**
- Removed: `https://api.anthropic.com`
- Added: `http://localhost:5173`

### 5. Dependency Changes (`packages/backend/package.json`)

**Removed:**
- `@anthropic-ai/sdk: ^0.32.1`

### 6. Error Handling and Suggestions

- Enhanced error handling for proxy API responses
- Proper handling of suggestions array from proxy responses
- Automatic retry logic for 401 (Unauthorized) responses
- Comprehensive error logging

## Configuration

### Development Environment
- Proxy server URL: `http://localhost:5173`
- Configured in `AIService.getProxyUrl()` method

### Production Environment
To deploy to production:
1. Update the `getProxyUrl()` method in `AIService` to return your production proxy URL
2. Update `manifest.json` to include your production domain in `allowedDomains`
3. Ensure your proxy server is deployed and accessible

## Usage

The plugin now automatically:
1. Fetches JWT tokens when needed
2. Makes authenticated requests to the proxy server
3. Handles token refresh automatically
4. Processes AI responses with enhanced error handling

## Testing

Build the plugin:
```bash
cd apps/plugin
pnpm build
```

The build should complete successfully without any Anthropic SDK dependencies.

## Security Considerations

1. **Token Storage:** JWT tokens are stored securely in Figma's clientStorage
2. **Token Expiry:** Tokens automatically expire after 15 minutes
3. **Network Access:** Limited to specified domains in manifest.json
4. **Error Handling:** Sensitive information is not exposed in error messages

## Troubleshooting

### Common Issues:

1. **Network Access Denied:**
   - Ensure proxy URL is added to `manifest.json` allowedDomains
   - Check that proxy server is running and accessible

2. **Authentication Failures:**
   - Verify proxy server `/api/auth/token` endpoint is working
   - Check proxy server logs for authentication issues

3. **Build Errors:**
   - Ensure `@anthropic-ai/sdk` dependency is completely removed
   - Run `pnpm install` to clean up dependencies

### Debug Logging:
The service includes comprehensive console logging for:
- Token fetch attempts
- API request failures
- Authentication errors
- Suggestion messages from proxy responses
