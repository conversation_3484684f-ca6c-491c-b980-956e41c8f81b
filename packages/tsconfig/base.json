{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"target": "ESNext", "composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "typeRoots": ["./node_modules/@types", "./node_modules/@figma"]}, "exclude": ["node_modules"]}