{"name": "plugin-ui", "version": "0.0.0", "private": true, "license": "MIT", "sideEffects": false, "main": "./src/index.tsx", "types": "./src/index.tsx", "scripts": {"lint": "eslint \"src/**/*.ts*\""}, "dependencies": {"@phosphor-icons/react": "^2.1.7", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/react-syntax-highlighter": "15.5.13", "copy-to-clipboard": "^3.3.3", "react": "^18.3.1", "react-syntax-highlighter": "^15.6.1", "tailwindcss": "3.4.6", "tweakpane": "^4.0.5"}, "devDependencies": {"eslint": "^9.17.0", "eslint-config-custom": "workspace:*", "tsconfig": "workspace:*", "types": "workspace:*", "typescript": "^5.7.2"}}