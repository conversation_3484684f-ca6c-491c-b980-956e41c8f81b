import {
  Framework,
  LocalCodegenPreferenceOptions,
  PluginSettings,
  SelectPreferenceOptions,
} from 'types';
import { useState } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { coldarkDark as theme } from 'react-syntax-highlighter/dist/esm/styles/prism';
import copy from 'copy-to-clipboard';
import { Copy, Check } from '@phosphor-icons/react';

interface CodePanelProps {
  code: string;
  selectedFramework: Framework;
  settings: PluginSettings | null;
  preferenceOptions: LocalCodegenPreferenceOptions[];
  selectPreferenceOptions: SelectPreferenceOptions[];
  onPreferenceChanged: (key: string, value: boolean | string) => void;
  isLoading: boolean;
}

const CodePanel = (props: CodePanelProps) => {
  const [isPressed, setIsPressed] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const [syntaxHovered, setSyntaxHovered] = useState(false);
  const { code, isLoading } = props;
  const isEmpty = code === '';

  const handleButtonClick = () => {
    setIsPressed(true);
    setIsCopied(true);
    copy(code);

    setTimeout(() => setIsPressed(false), 250);
    // Reset copied state after 3 seconds
    setTimeout(() => setIsCopied(false), 3000);
  };

  const handleButtonHover = () => setSyntaxHovered(true);
  const handleButtonLeave = () => setSyntaxHovered(false);

  return (
    <div className="w-full h-full flex flex-col">
      <div
        className="relative h-full overflow-y-auto"
        onMouseEnter={handleButtonHover}
        onMouseLeave={handleButtonLeave}
      >
        {isEmpty ? (
          <div className="flex items-center justify-center h-full bg-[#1B1B1B] text-white/50">
            {isLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500"></div>
                <span>Converting to code...</span>
              </div>
            ) : (
              <h3>No layer is selected. Please select a layer.</h3>
            )}
          </div>
        ) : (
          <>
            {/* Add loading overlay for non-empty state */}
            {isLoading && (
              <div className="absolute inset-0 z-20 bg-black/20 backdrop-blur-sm flex items-center justify-center">
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500"></div>
                  <span className="text-white">Processing code...</span>
                </div>
              </div>
            )}
            {/* Copy button overlay */}
            <div
              className={`absolute top-2 right-2 z-10 transition-opacity duration-200 ${
                syntaxHovered ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <button
                className={`px-3 py-1.5 text-sm font-semibold bg-neutral-800/80 backdrop-blur-sm text-white shadow-sm hover:bg-neutral-700/80 transition-all duration-300 inline-flex items-center gap-2 ${
                  isPressed ? 'ring-2 ring-orange-500 ring-opacity-50' : ''
                }`}
                onClick={handleButtonClick}
              >
                {isCopied ? <Check size={16} /> : <Copy size={16} />}
              </button>
            </div>

            {/* Syntax highlighter */}
            <div
              className={`ring-orange-600 transition-all duration-300 ${
                syntaxHovered ? 'ring-2' : 'ring-0'
              }`}
            >
              <SyntaxHighlighter
                language="dart"
                style={theme}
                customStyle={{
                  fontSize: 12,
                  margin: 0,
                  padding: '1rem',
                  backgroundColor: syntaxHovered ? '#1E2B1A' : '#1B1B1B',
                  transitionProperty: 'all',
                  transitionTimingFunction: 'ease',
                  transitionDuration: '0.2s',
                  opacity: isLoading ? 0.5 : 1,
                }}
              >
                {code}
              </SyntaxHighlighter>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
export default CodePanel;
