import { useEffect, useRef } from 'react';
import { Pane } from 'tweakpane';
import {
  Framework,
  HTMLPreview,
  LinearGradientConversion,
  PluginSettings,
  SolidColorConversion,
  Warning,
} from 'types';
import {
  preferenceOptions,
  selectPreferenceOptions,
} from './codegenPreferenceOptions';
import CodePanel from './components/CodePanel';
import Preview from './components/Preview';

type PluginUIProps = {
  code: string;
  htmlPreview: HTMLPreview;
  warnings: Warning[];
  selectedFramework: Framework;
  setSelectedFramework: (framework: Framework) => void;
  settings: PluginSettings | null;
  onPreferenceChanged: (key: string, value: boolean | string) => void;
  colors: SolidColorConversion[];
  gradients: LinearGradientConversion[];
  isLoading: boolean;
  config: Record<string, any>;
};

const frameworks: Framework[] = ['Tailwind'];

export const PluginUI = (props: PluginUIProps) => {
  const isEmpty = props.code === '';
  const warnings = props.warnings ?? [];
  const paneContainerRef = useRef<HTMLDivElement>(null);
  const paneRef = useRef<Pane | null>(null);
  const hasTweakpaneConfig =
    props.config && Object.keys(props.config).length > 0;

  useEffect(() => {
    const cleanup = () => {
      if (paneRef.current) {
        paneRef.current.dispose();
        paneRef.current = null;
      }
    };

    const initializePane = () => {
      cleanup();

      if (
        !props.config ||
        Object.keys(props.config).length === 0 ||
        !paneContainerRef.current
      ) {
        console.log('No config or no container');
        return;
      }

      try {
        const pane = new Pane({
          container: paneContainerRef.current,
          title: 'Controls',
        });
        paneRef.current = pane;

        Object.entries(props.config).forEach(([folderName, folderControls]) => {
          const folderPane = pane.addFolder({ title: folderName });

          Object.entries(folderControls as Record<string, any>).forEach(
            ([controlName, control]) => {
              const params = { [controlName]: control.value };

              folderPane
                .addBinding(params, controlName, {
                  min: control.min,
                  max: control.max,
                  step: control.step,
                  label: controlName,
                })
                .on('change', (ev) => {});
            },
          );
        });
      } catch (error) {
        console.error('Error initializing Tweakpane:', error);
      }
    };

    initializePane();

    return cleanup;
  }, [props.config]);

  return (
    <div className="flex flex-col h-full dark:text-white">
      <div
        style={{
          height: 1,
          width: '100%',
          backgroundColor: 'rgba(255,255,255,0.12)',
        }}
      ></div>
      <div className="flex flex-col h-full overflow-hidden">
        <div className="flex flex-row items-start gap-4 dark:bg-transparent h-full">
          <div className="flex flex-col w-1/2 h-screen overflow-scroll">
            <div className="flex flex-col h-full">
              {hasTweakpaneConfig && (
                <div ref={paneContainerRef} className="w-full" />
              )}

              <div className="flex-grow overflow-auto">
                <CodePanel
                  code={props.code}
                  selectedFramework={props.selectedFramework}
                  preferenceOptions={preferenceOptions}
                  selectPreferenceOptions={selectPreferenceOptions}
                  settings={props.settings}
                  onPreferenceChanged={props.onPreferenceChanged}
                  isLoading={props.isLoading}
                />
              </div>

              <button
                onClick={() => window.open('https://www.fiddle.is', '_blank')}
                className="group relative bg-black/10 p-2 text-white text-sm sticky bottom-0 w-full hover:bg-black/5 transition-colors"
              >
                Join Fiddle Waitlist ↗
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-black/90 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap flex flex-col">
                  <img
                    src="https://i.imgur.com/qxZL3ns.gif"
                    className="w-full h-auto rounded-md"
                  />
                  <span>The IDE for creatives</span>
                </div>
              </button>
            </div>
          </div>

          {isEmpty === false && props.htmlPreview && (
            <div className="flex flex-col w-1/2 h-full">
              <Preview htmlPreview={props.htmlPreview} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
