{"name": "types", "version": "0.0.0", "private": true, "license": "GPT-3", "sideEffects": false, "main": "./src/index.ts", "types": "./src/index.ts", "files": ["dist/**"], "scripts": {"lint": "eslint \"src/**/*.ts*\""}, "dependencies": {"@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "tsconfig": "workspace:*"}, "devDependencies": {"@figma/plugin-typings": "^1.112.0", "eslint": "^9.17.0", "eslint-config-custom": "workspace:*", "typescript": "^5.7.2"}}