import {
  ConversionMessage,
  EmptyMessage,
  ErrorMessage,
  PluginSettings,
  SettingsChangedMessage,
} from 'types';
import { AIService } from './services/aiService';

const aiService = new AIService();

export const postBackendMessage = figma.ui.postMessage;

export const postEmptyMessage = () =>
  postBackendMessage({ type: 'empty' } as EmptyMessage);

export const postConversionComplete = async (
  conversionData: ConversionMessage | Omit<ConversionMessage, 'type'>,
  figmaSelection: SceneNode,
) => {
  try {
    const bytes = await figmaSelection.exportAsync({
      format: 'PNG',
      constraint: { type: 'SCALE', value: 2 },
    });
    const base64Image = figma.base64Encode(bytes);
    const imageUrl = `data:image/png;base64,${base64Image}`;

    const { cleanCode, suggestions } = await aiService.cleanCode(
      conversionData.code,
      imageUrl,
    );

    const sections = parseAIResponse(cleanCode);

    // If there are error suggestions, log them and potentially show to user
    if (suggestions && suggestions.length > 0) {
      console.warn('AI Service suggestions:', suggestions);
      // You could add these suggestions to the warnings array if needed
    }

    postBackendMessage({
      ...conversionData,
      code: sections.code,
      config: sections.config || {},
      htmlPreview: {
        content: sections.code,
        size: figmaSelection.absoluteBoundingBox || { width: 500, height: 500 },
      },
      image: imageUrl,
      type: 'code',
    });
  } catch (error) {
    console.error('AI processing failed:', error);
    postBackendMessage({
      ...conversionData,
      type: 'code',
      config: {},
    });
  }
};

function parseAIResponse(response: string) {
  const codeSectionMatch = response.match(
    /---\s*CODE\s*---([\s\S]*?)(?=---\s*TWEAKPANE\s*---|$)/,
  );
  const configSectionMatch = response.match(/---\s*TWEAKPANE\s*---([\s\S]*?)$/);

  const code = codeSectionMatch
    ? codeSectionMatch[1]
        .replace(/```(?:html|language=html)?\n/g, '')
        .replace(/```(?:language=)?\n?$/g, '')
        .trim()
    : '';

  let config = {};

  if (configSectionMatch) {
    try {
      const configText = configSectionMatch[1]
        .replace(/```(?:json|language=json)?\n/g, '')
        .replace(/```\s*$/g, '')
        .replace(/^\s*|\s*$/g, '')
        .trim();

      if (configText && configText !== '{}') {
        config = JSON.parse(configText);
      }
    } catch (e) {
      console.error('Failed to parse config:', e);
      console.error('Problematic config text:', configSectionMatch[1]);
    }
  }

  return {
    code,
    config: config || {}, // Ensure config is always an object
  };
}

export const postError = (error: string) =>
  postBackendMessage({ type: 'error', error } as ErrorMessage);

export const postSettingsChanged = (settings: PluginSettings) =>
  postBackendMessage({
    type: 'pluginSettingsChanged',
    settings,
  } as SettingsChangedMessage);
