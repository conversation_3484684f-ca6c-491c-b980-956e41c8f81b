interface CleanCodeResponse {
  cleanCode: string;
  suggestions?: string[];
}

interface ProxyResponse {
  cleanCode: string;
  suggestions?: string[];
}

interface AuthResponse {
  token: string;
}

/*
 * Figma plugins run in a sandboxed environment, not in Node.js.
 * The backend code gets bundled and runs in Figma's JavaScript context,
 * which doesn't have access to Node.js APIs like process.env.
 */

export class AIService {
  private proxyBaseUrl: string;
  private token: string | null = null;
  private tokenExpiry: number = 0;

  constructor() {
    // Use localhost for development, can be configured for production
    // In production, this should be set to your deployed proxy server URL
    this.proxyBaseUrl = this.getProxyUrl();
  }

  private getProxyUrl(): string {
    // For development, use localhost
    // For production, you can modify this to use a different URL
    // or read from a configuration stored in Figma's clientStorage
    return 'http://localhost:5173';
  }

  private async getValidToken(): Promise<string> {
    // Check if we have a valid token that hasn't expired
    if (this.token && Date.now() < this.tokenExpiry) {
      return this.token;
    }

    // Try to get stored token from Figma's clientStorage
    const storedTokenData =
      await figma.clientStorage.getAsync('proxyAuthToken');
    if (storedTokenData) {
      const { token, expiry } = JSON.parse(storedTokenData);
      if (Date.now() < expiry) {
        this.token = token;
        this.tokenExpiry = expiry;
        return token;
      }
    }

    // Fetch new token from proxy server
    return await this.fetchNewToken();
  }

  private async fetchNewToken(): Promise<string> {
    try {
      const response = await fetch(`${this.proxyBaseUrl}/api/auth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch auth token: ${response.status} ${response.statusText}`,
        );
      }

      const authData: AuthResponse = await response.json();

      // Store token with 15-minute expiry (minus 1 minute buffer)
      const expiry = Date.now() + 14 * 60 * 1000;
      this.token = authData.token;
      this.tokenExpiry = expiry;

      // Store in Figma's clientStorage for persistence
      await figma.clientStorage.setAsync(
        'proxyAuthToken',
        JSON.stringify({
          token: authData.token,
          expiry: expiry,
        }),
      );

      return authData.token;
    } catch (error) {
      console.error('Failed to fetch auth token:', error);
      throw new Error('Authentication failed');
    }
  }

  async cleanCode(code: string, imageUrl: string): Promise<CleanCodeResponse> {
    try {
      const token = await this.getValidToken();
      const base64Image = imageUrl.split(',')[1]; // Remove the data:image/png;base64, prefix

      const requestBody = {
        code,
        imageUrl: base64Image,
      };

      const response = await fetch(`${this.proxyBaseUrl}/api/clean-code`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        // If unauthorized, try to refresh token and retry once
        if (response.status === 401) {
          console.log('Token expired, refreshing...');
          const newToken = await this.fetchNewToken();

          const retryResponse = await fetch(
            `${this.proxyBaseUrl}/api/clean-code`,
            {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${newToken}`,
              },
              body: JSON.stringify(requestBody),
            },
          );

          if (!retryResponse.ok) {
            throw new Error(
              `Proxy API error: ${retryResponse.status} ${retryResponse.statusText}`,
            );
          }

          const retryData: ProxyResponse = await retryResponse.json();
          return {
            cleanCode: retryData.cleanCode,
            suggestions: retryData.suggestions || [],
          };
        }

        throw new Error(
          `Proxy API error: ${response.status} ${response.statusText}`,
        );
      }

      const data: ProxyResponse = await response.json();

      return {
        cleanCode: data.cleanCode,
        suggestions: data.suggestions || [],
      };
    } catch (error) {
      console.error('AI Service error:', error);
      return {
        cleanCode: code,
        suggestions: [
          `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ],
      };
    }
  }
}

const prompt = `
This project was imported from Figma. Since it was converted, interactive elements are divs. Please refer to the image to identify what components are interactive and which are not.
Refer to this information and recreate the UI with simple and straightform interactions where it is suitable (ex: make file uploads, make buttons clickable etc)
    Pay special attention to components, styles, and design tokens.
Also add minor microinteractions.
IMPORTANT: Pay careful attention to the type of font used — serif, sans, italics etc.
      IMPORTANT: Use the image to ensure you have understood the context of the design and recreate it precisely.
      IMPORTANT: Pay careful attention to the code shared!! If there are images, use the exact same source while recreating the element! DO NOT RECREATE IT

You are a Micro-Animation Expert. Your job is to create simple beautiful micro-animations for the selected element based on the users prompt.
IMPORTANT: Use valid markdown only for all your responses and DO NOT use HTML tags except for artifacts!

ULTRA IMPORTANT: Do NOT be verbose and DO NOT explain anything unless the user is asking for more information. That is VERY important.

RESPONSE FORMAT:
Your response should have two clearly separated sections:

--- CODE ---
[The cleaned code here]

--- TWEAKPANE ---
[JSON configuration for TWEAKPANE GUI]

The TWEAKPANE config should be valid JSON that can be directly used with TWEAKPANE's useControls hook.
--- CODE ---
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Star Rating</title>
  <style>
    .star {
      width: 40px;
      height: 40px;
      cursor: pointer;
      transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .star.filled {
      color: #facc15; /* Tailwind's yellow-400 */
    }

    .star.empty {
      color: #d1d5db; /* Tailwind's gray-300 */
    }

    .flex {
      display: flex;
    }
  </style>
</head>
<body>
  <div id="star-rating" class="flex" data-config-id="star-rating-animation"></div>

  <script>
    const max = 5;
    let value = 0;
    let hoverValue = 0;

    let animationParams = {
      duration: 0.3,
      easing: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
      scale: 1.2,
    };

    const container = document.getElementById('star-rating');

    function renderStars() {
      container.innerHTML = '';
      for (let i = 1; i <= max; i++) {
        const star = document.createElementNS("http://www.w3.org/2000/svg", "svg");
        star.setAttribute("xmlns", "http://www.w3.org/2000/svg");
        star.setAttribute("viewBox", "0 0 24 24");
        star.setAttribute("fill", (hoverValue || value) >= i ? "currentColor" : "none");
        star.setAttribute("stroke", "currentColor");
        star.classList.add("star", (hoverValue || value) >= i ? "filled" : "empty");
        star.style.transform = (hoverValue || value) >= i ? \`scale(\${animationParams.scale})\` : 'scale(1)';
        star.style.transition = \`transform \${animationParams.duration}s \${animationParams.easing}\`;

        star.innerHTML = \`
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="\${(hoverValue || value) >= i ? 0 : 2}"
            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
          />
        \`;

        star.addEventListener('mouseover', () => {
          hoverValue = i;
          renderStars();
        });

        star.addEventListener('mouseleave', () => {
          hoverValue = 0;
          renderStars();
        });

        star.addEventListener('click', () => {
          value = i;
          renderStars();
        });

        container.appendChild(star);
      }
    }

    container.addEventListener('animation:update', (event) => {
      animationParams = event.detail;
      renderStars();
    });

    renderStars();
  </script>
</body>
</html>


--- TWEAKPANE ---
"Animation": {
  "scale": {
    "value": 1.2,
    "min": 1,
    "max": 2,
    "step": 0.1
  },
  "duration": {
    "value": 0.3,
    "min": 0.1,
    "max": 1,
    "step": 0.1
  },
  "easing": {
    "value": "cubic-bezier(0.34, 1.56, 0.64, 1)",
    "options": {
      "Bouncy": "cubic-bezier(0.34, 1.56, 0.64, 1)",
      "Smooth": "cubic-bezier(0.4, 0, 0.2, 1)",
      "Spring": "cubic-bezier(0.68, -0.6, 0.32, 1.6)"
    }
  }
}

IMPORTANT: The tweakpane config and the file changes need to be wrapped in 1 <fiddleArtifact>
IMPORTANT: Don't add a configRegistry, it is not needed. And do not run a dev server please.
IMPORTANT: Make sure the configId is unique and matches the data-config-id of the component, use kebab-case
IMPORTANT: make the changes only for the filepath specified. So if it says variant1/rest/of/path dont change variant2 or 3 or anything else
IMPORTANT: the range in the config should be a noticeable difference. BUT the animation should be subtle, elegant and smooth.`;
