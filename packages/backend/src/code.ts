import { PluginSettings } from 'types';
import { convertNodesToAltNodes } from './altNodes/altConversion';
import {
  addWarning,
  clearWarnings,
  warnings,
} from './common/commonConversionWarnings';
import { convertToCode } from './common/retrieveUI/convertToCode';
import {
  retrieveGenericLinearGradients as retrieveGenericGradients,
  retrieveGenericSolidUIColors,
} from './common/retrieveUI/retrieveColors';
import { generateHTMLPreview } from './html/htmlMain';
import {
  postBackendMessage,
  postConversionComplete,
  postEmptyMessage,
} from './messaging';

export const run = async (settings: PluginSettings) => {
  clearWarnings();
  const { framework } = settings;
  const selection = figma.currentPage.selection;

  if (selection.length > 1) {
    addWarning(
      'Ungrouped elements may have incorrect positioning. If this happens, try wrapping the selection in a Frame or Group.',
    );
  }

  const convertedSelection = convertNodesToAltNodes(selection, null);

  if (convertedSelection.length === 0) {
    postEmptyMessage();
    return;
  }

  postBackendMessage({ type: 'conversion_start' });

  const code = await convertToCode(convertedSelection, settings);
  const htmlPreview = await generateHTMLPreview(
    convertedSelection,
    settings,
    code,
  );
  const colors = retrieveGenericSolidUIColors(framework);
  const gradients = retrieveGenericGradients(framework);

  postConversionComplete(
    {
      code,
      htmlPreview,
      colors,
      gradients,
      settings,
      warnings: [...warnings],
    },
    selection[0],
  );
};
