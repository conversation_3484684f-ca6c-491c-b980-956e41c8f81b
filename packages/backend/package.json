{"name": "backend", "version": "0.0.0", "private": true, "license": "GPT-3", "sideEffects": false, "main": "./src/index.ts", "types": "./src/index.ts", "files": ["dist/**"], "scripts": {"lint": "eslint \"src/**/*.ts*\""}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@figma/plugin-typings": "^1.105.0", "react": "18.3.1", "react-dom": "18.3.1", "types": "workspace:*"}, "devDependencies": {"@types/node": "^20.17.10", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "eslint": "^9.17.0", "eslint-config-custom": "workspace:*", "tsconfig": "workspace:*", "tsup": "^8.3.5", "typescript": "^5.7.2"}}