import { useEffect, useState } from 'react';
import { PluginUI } from 'plugin-ui';
import {
  Framework,
  PluginSettings,
  ConversionMessage,
  Message,
  HTMLPreview,
  LinearGradientConversion,
  SolidColorConversion,
  ErrorMessage,
  SettingsChangedMessage,
  Warning,
} from 'types';
import { postUISettingsChangingMessage } from './messaging';

interface AppState {
  code: string;
  selectedFramework: Framework;
  isLoading: boolean;
  htmlPreview: HTMLPreview;
  settings: PluginSettings | null;
  colors: SolidColorConversion[];
  gradients: LinearGradientConversion[];
  warnings: Warning[];
  config: Record<string, any>;
}

const emptyPreview = { size: { width: 0, height: 0 }, content: '' };
export default function App() {
  const [state, setState] = useState<AppState>({
    code: '',
    selectedFramework: 'HTML',
    isLoading: false,
    htmlPreview: emptyPreview,
    settings: null,
    colors: [],
    gradients: [],
    warnings: [],
    config: {},
  });

  const rootStyles = getComputedStyle(document.documentElement);
  const figmaColorBgValue = rootStyles
    .getPropertyValue('--figma-color-bg')
    .trim();

  useEffect(() => {
    window.onmessage = (event: MessageEvent) => {
      const untypedMessage = event.data.pluginMessage as Message;

      switch (untypedMessage.type) {
        case 'conversion_start':
          setState((prev) => ({ ...prev, isLoading: true }));
          break;

        case 'code':
          const conversionMessage = untypedMessage as ConversionMessage;

          setState((prevState) => ({
            ...prevState,
            ...conversionMessage,
            config: conversionMessage.config || {},
            isLoading: false,
            selectedFramework: conversionMessage.settings.framework,
          }));
          break;

        case 'pluginSettingChanged':
          const settingsMessage = untypedMessage as SettingsChangedMessage;
          setState((prevState) => ({
            ...prevState,
            settings: settingsMessage.settings,
            selectedFramework: settingsMessage.settings.framework,
          }));
          break;

        case 'empty':
          // const emptyMessage = untypedMessage as EmptyMessage;
          setState((prevState) => ({
            ...prevState,
            code: '',
            htmlPreview: emptyPreview,
            warnings: [],
            colors: [],
            gradients: [],
          }));
          break;

        case 'error':
          const errorMessage = untypedMessage as ErrorMessage;

          setState((prevState) => ({
            ...prevState,
            colors: [],
            gradients: [],
            code: `Error :(\n// ${errorMessage.error}`,
          }));
          break;
        default:
          break;
      }
    };

    return () => {
      window.onmessage = null;
    };
  }, []);

  useEffect(() => {
    if (state.selectedFramework === null) {
      const timer = setTimeout(
        () => setState((prevState) => ({ ...prevState, isLoading: true })),
        300,
      );
      return () => clearTimeout(timer);
    } else {
      setState((prevState) => ({ ...prevState, isLoading: false }));
    }
  }, [state.selectedFramework]);

  if (state.selectedFramework === null) {
    return state.isLoading ? (
      <div className="w-full h-96 justify-center text-center items-center dark:text-white text-lg">
        Loading Plugin...
      </div>
    ) : null;
  }

  const handleFrameworkChange = (updatedFramework: Framework) => {
    setState((prevState) => ({
      ...prevState,
      // code: "// Loading...",
      selectedFramework: updatedFramework,
    }));
    postUISettingsChangingMessage('framework', updatedFramework, {
      targetOrigin: '*',
    });
  };

  return (
    <div className={`${figmaColorBgValue === '#ffffff' ? '' : 'dark'}`}>
      <PluginUI
        code={state.code}
        warnings={state.warnings}
        selectedFramework={state.selectedFramework}
        setSelectedFramework={handleFrameworkChange}
        htmlPreview={state.htmlPreview}
        settings={state.settings}
        onPreferenceChanged={(key: string, value: boolean | string) =>
          postUISettingsChangingMessage(key, value, { targetOrigin: '*' })
        }
        colors={state.colors}
        gradients={state.gradients}
        isLoading={state.isLoading}
        config={state.config}
      />
    </div>
  );
}
