import { Message, SettingWillChangeMessage, UIMessage } from 'types';

if (!parent || !parent.postMessage) {
  throw new Error('parent.postMessage() is not defined');
}

// Add a loading state manager
let isProcessing = false;
const listeners: ((loading: boolean) => void)[] = [];

export const addLoadingListener = (callback: (loading: boolean) => void) => {
  listeners.push(callback);
  return () => {
    const index = listeners.indexOf(callback);
    if (index > -1) listeners.splice(index, 1);
  };
};

const setLoading = (loading: boolean) => {
  isProcessing = loading;
  listeners.forEach((listener) => listener(loading));
};

const postMessage = (
  message: UIMessage,
  options?: WindowPostMessageOptions,
) => {
  setLoading(true);
  parent.postMessage(message, options);
};

// Listen for responses from the plugin
window.onmessage = (event) => {
  if (event.data.pluginMessage) {
    setLoading(false);
  }
};

export const postUIMessage = (
  message: Message,
  options?: WindowPostMessageOptions,
) => postMessage({ pluginMessage: message }, options);

export const postUISettingsChangingMessage = <T>(
  key: string,
  value: T,
  options?: WindowPostMessageOptions,
) => {
  const message: SettingWillChangeMessage<T> = {
    type: 'pluginSettingWillChange',
    key,
    value,
  };
  postUIMessage(message, options);
};

export type UIMessage = {
  pluginMessage: Message | SettingWillChangeMessage<any>;
};

// Export current loading state
export const getIsProcessing = () => isProcessing;
