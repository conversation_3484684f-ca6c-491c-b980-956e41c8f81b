import {
  convertIntoNodes,
  htmlMain,
  postSettingsChanged,
  run,
  tailwindMain,
} from 'backend';
import { retrieveGenericSolidUIColors } from 'backend/src/common/retrieveUI/retrieveColors';
import { htmlCodeGenTextStyles } from 'backend/src/html/htmlMain';
import { PluginSettings, SettingWillChangeMessage } from 'types';
import { tailwindCodeGenTextStyles } from './../../../packages/backend/src/tailwind/tailwindMain';

let userPluginSettings: PluginSettings;

export const defaultPluginSettings: PluginSettings = {
  framework: 'Tailwind',
  jsx: false,
  optimizeLayout: true,
  showLayerNames: true,
  inlineStyle: false,
  responsiveRoot: false,
  flutterGenerationMode: 'snippet',
  swiftUIGenerationMode: 'snippet',
  roundTailwindValues: false,
  roundTailwindColors: false,
  customTailwindColors: false,
};

// A helper type guard to ensure the key belongs to the PluginSettings type
function isKeyOfPluginSettings(key: string): key is keyof PluginSettings {
  return key in defaultPluginSettings;
}

const getUserSettings = async () => {
  const possiblePluginSrcSettings =
    (await figma.clientStorage.getAsync('userPluginSettings')) ?? {};

  const updatedPluginSrcSettings = {
    ...defaultPluginSettings,
    ...Object.keys(defaultPluginSettings).reduce((validSettings, key) => {
      if (
        isKeyOfPluginSettings(key) &&
        key in possiblePluginSrcSettings &&
        typeof possiblePluginSrcSettings[key] ===
          typeof defaultPluginSettings[key]
      ) {
        validSettings[key] = possiblePluginSrcSettings[key] as any;
      }
      return validSettings;
    }, {} as Partial<PluginSettings>),
  };

  userPluginSettings = updatedPluginSrcSettings as PluginSettings;
};

const initSettings = async () => {
  await getUserSettings();
  postSettingsChanged(userPluginSettings);
  safeRun(userPluginSettings);
};

const safeRun = (settings: PluginSettings) => {
  try {
    run(settings);
  } catch (e) {
    if (e && typeof e === 'object' && 'message' in e) {
      const error = e as Error;
      console.log('error: ', error.stack);
      figma.ui.postMessage({ type: 'error', error: error.message });
    }
  }
};

const standardMode = async () => {
  figma.showUI(__html__, { width: 650, height: 400, themeColors: true });
  await initSettings();

  // Listen for selection changes
  figma.on('selectionchange', () => {
    safeRun(userPluginSettings);
  });

  // Listen for document changes
  figma.on('documentchange', () => {
    safeRun(userPluginSettings);
  });

  figma.ui.onmessage = (msg) => {
    if (msg.type === 'pluginSettingWillChange') {
      const { key, value } = msg as SettingWillChangeMessage<unknown>;
      (userPluginSettings as any)[key] = value;
      figma.clientStorage.setAsync('userPluginSettings', userPluginSettings);
      safeRun(userPluginSettings);
    }
  };
};

const codegenMode = async () => {
  // figma.showUI(__html__, { visible: false });
  await getUserSettings();

  figma.codegen.on('generate', async ({ language, node }) => {
    const convertedSelection = convertIntoNodes([node], null);
    const blocks: CodegenResult[] = [];

    try {
      switch (language) {
        case 'html':
          return [
            {
              title: `Code`,
              code: await Promise.resolve(
                htmlMain(
                  convertedSelection,
                  { ...userPluginSettings, jsx: false },
                  true,
                ),
              ),
              language: 'HTML',
            },
            {
              title: `Text Styles`,
              code: await Promise.resolve(
                htmlCodeGenTextStyles(userPluginSettings),
              ),
              language: 'HTML',
            },
          ];
        case 'html_jsx':
          return [
            {
              title: `Code`,
              code: await Promise.resolve(
                htmlMain(
                  convertedSelection,
                  { ...userPluginSettings, jsx: true },
                  true,
                ),
              ),
              language: 'HTML',
            },
            {
              title: `Text Styles`,
              code: await Promise.resolve(
                htmlCodeGenTextStyles(userPluginSettings),
              ),
              language: 'HTML',
            },
          ];
        case 'tailwind':
        case 'tailwind_jsx':
          return [
            {
              title: `Code`,
              code: await Promise.resolve(
                tailwindMain(convertedSelection, {
                  ...userPluginSettings,
                  jsx: language === 'tailwind_jsx',
                }),
              ),
              language: 'HTML',
            },
            {
              title: `Tailwind Colors`,
              code: retrieveGenericSolidUIColors('Tailwind')
                .map((d) => {
                  let str = `${d.hex};`;
                  if (d.colorName !== d.hex) {
                    str += ` // ${d.colorName}`;
                  }
                  if (d.meta) {
                    str += ` (${d.meta})`;
                  }
                  return str;
                })
                .join('\n'),
              language: 'JAVASCRIPT',
            },
            {
              title: `Text Styles`,
              code: await Promise.resolve(tailwindCodeGenTextStyles()),
              language: 'HTML',
            },
          ];
        default:
          break;
      }
    } catch (error) {
      console.error('Codegen error:', error);
      if (error instanceof Error) {
        // Add a block with the error message
        blocks.push({
          title: 'Error',
          code: `An error occurred: ${error.message}\n\nPlease check if you have access to all elements in your selection.`,
          language: 'PLAINTEXT',
        });
      }
    }

    return blocks;
  });
};

switch (figma.mode) {
  case 'default':
  case 'inspect':
    standardMode();
    break;
  case 'codegen':
    codegenMode();
    break;
  default:
    break;
}
